import { notFound } from 'next/navigation'

import { posts } from '#velite'
import { MDXContent } from "@/components/mdx-components";
import Link from "next/link";
import {Button} from "@/components/ui/button";
import React from "react";

interface PostProps {
  params: Promise<{ slug: string }>
}

function getPostBySlug(slug: string) {
  return posts.find(post => post.slug === slug)
}

export default async function PostPage({ params }: PostProps) {
  const { slug } = await params
  const post = getPostBySlug(slug)

  if (post == null) {
    notFound()
  }
  else {
    const date = new Date(post.updated).toLocaleDateString(undefined, {
      year: "numeric",
      month: "long",
      day: "numeric",
      hour: "numeric",
      minute: "numeric",
    })

    return (
      <article className="flex flex-col 2xl:max-w-[900px] max-w-[700px] "> {/*[min-width:1400px]:max-w-[900px]*/}
        <div className="flex flex-col gap-2 items-center mb-8">
          <h1 className="text-4xl font-bold text-center">{post.title}</h1>
          <p className="">{post.description}</p>
          <p><b>{post.metadata.readingTime}</b> minute read</p>
          {/*<p>{post.categories}</p>*/}
        </div>

        <MDXContent code={post.body} />

        <footer className="flex flex-row">
          <Button variant="link" asChild className="px-0">
            <Link href="">
              Last updated: {date}
            </Link>
          </Button>
        </footer>
      </article>
    )
  }
}