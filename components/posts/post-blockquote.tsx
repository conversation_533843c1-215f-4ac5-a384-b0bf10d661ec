import * as React from "react"
import { cn } from "@/lib/utils"
import {
  Info,
  CircleQuestionMark,
  CircleCheck,
  TriangleAlert,
  OctagonAlert
} from "lucide-react";

const styles = {
  info: "bg-blue-100 text-blue-900",
  question: "bg-gray-100 text-gray-900",
  success: "bg-green-100 text-green-900",
  warning: "bg-yellow-100 text-yellow-900",
  error: "bg-red-100 text-red-900",
}

const icons = {
  info: Info,
  question: CircleQuestionMark,
  success: CircleCheck,
  warning: <PERSON><PERSON>lert,
  error: OctagonAlert,
}

interface PostBlockquoteProps {
  type: blockquoteType
  children: React.ReactNode
  className?: string
}

interface blockquoteType = [
  "info",
  "question",
  "success",
  "warning",
  "error",
]

const IconComponent = icons[blockquoteType]

export const PostBlockquote = ({ type, children, className, ...props }: PostBlockquoteProps) => (
  <div className={cn("w-full my-2 rounded-lg border-2 p-4 border-primary", styles[type], className)} {...props}>
    <div className="flex items-center">
      <IconComponent className={cn("w-5 h-5 mr-2 flex-shrink-0", iconClassName)} />
      <div>{children}</div>
    </div>
  </div>
)
