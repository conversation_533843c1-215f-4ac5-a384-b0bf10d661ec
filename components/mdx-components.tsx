import * as runtime from 'react/jsx-runtime'
import React from "react";

import { PostLink } from "@/components/posts/post-link";
import { PostP } from "@/components/posts/post-p";
import { PostBlockquote } from "@/components/posts/post-blockquote";


export const sharedComponents = {
  // Replace <a> with PostLink
  a: PostLink,
  p: PostP,
  blockquote: PostBlockquote
}

const useMDXComponent = (code: string) => {
  const fn = new Function(code)
  return fn({ ...runtime }).default
}

interface MDXProps {
  code: string
  components?: Record<string, React.ComponentType>
}

export const MDXContent = ({ code, components }: MDXProps) => {
  const Component = useMDXComponent(code)
  return <Component components={{ ...sharedComponents, ...components }} />
}
